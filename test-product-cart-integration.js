const mongoose = require('mongoose');
const Product = require('./models/product.model');
const Cart = require('./models/cart.model');
const User = require('./models/user.model');
const { getProduct } = require('./controllers/buyers/product.controller');

// Mock request and response objects
const createMockReq = (productId, userId = null) => ({
  params: { id: productId },
  user: userId ? { id: userId } : null
});

const createMockRes = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

const createMockNext = () => jest.fn();

// Test function to verify cart integration
async function testProductCartIntegration() {
  try {
    console.log('🧪 Testing Product Cart Integration...\n');

    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/everyfash-test');
    console.log('✅ Connected to database');

    // Find an active product with variations
    const product = await Product.findOne({ 
      status: 'active',
      'variations.0': { $exists: true }
    }).populate('variations');

    if (!product) {
      console.log('❌ No active product with variations found');
      return;
    }

    console.log(`📦 Testing with product: ${product.name} (${product._id})`);
    console.log(`🔧 Product has ${product.variations.length} variations`);

    // Find a test user (buyer)
    const user = await User.findOne({ role: 'user' });
    if (!user) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`👤 Testing with user: ${user.name} (${user._id})`);

    // Test 1: Get product without user (should not include cart info)
    console.log('\n🔍 Test 1: Anonymous user request');
    const req1 = createMockReq(product._id);
    const res1 = createMockRes();
    const next1 = createMockNext();

    await getProduct(req1, res1, next1);

    if (res1.json.mock.calls.length > 0) {
      const response1 = res1.json.mock.calls[0][0];
      const productData1 = response1.data.product;
      
      console.log(`✅ Response received for anonymous user`);
      console.log(`📊 Cart summary: ${productData1.cartSummary}`);
      
      // Check that variations don't have cart info
      const hasCartInfo = productData1.variations.some(v => v.inCart !== null);
      console.log(`🛒 Variations have cart info: ${hasCartInfo ? '❌ YES (should be NO)' : '✅ NO'}`);
    }

    // Test 2: Add some items to cart
    console.log('\n🛒 Test 2: Adding items to cart');
    let cart = await Cart.findOne({ user: user._id });
    if (!cart) {
      cart = await Cart.create({ user: user._id });
      console.log('✅ Created new cart');
    }

    // Add first two variations to cart
    const variation1 = product.variations[0];
    const variation2 = product.variations[1] || product.variations[0];

    await cart.addProduct({
      productId: product._id,
      variationId: variation1._id,
      quantity: 2
    });

    if (variation2._id.toString() !== variation1._id.toString()) {
      await cart.addProduct({
        productId: product._id,
        variationId: variation2._id,
        quantity: 1
      });
    }

    console.log(`✅ Added variations to cart`);
    console.log(`📦 Variation 1: ${variation1.color} ${variation1.size} (qty: 2)`);
    if (variation2._id.toString() !== variation1._id.toString()) {
      console.log(`📦 Variation 2: ${variation2.color} ${variation2.size} (qty: 1)`);
    }

    // Test 3: Get product with authenticated user (should include cart info)
    console.log('\n🔍 Test 3: Authenticated user request');
    const req2 = createMockReq(product._id, user._id);
    const res2 = createMockRes();
    const next2 = createMockNext();

    await getProduct(req2, res2, next2);

    if (res2.json.mock.calls.length > 0) {
      const response2 = res2.json.mock.calls[0][0];
      const productData2 = response2.data.product;
      
      console.log(`✅ Response received for authenticated user`);
      console.log(`📊 Cart summary:`, productData2.cartSummary);
      
      // Check variations for cart info
      console.log('\n🔍 Variation cart status:');
      productData2.variations.forEach((variation, index) => {
        const cartStatus = variation.inCart ? 
          `✅ In cart (qty: ${variation.inCart.quantity})` : 
          '❌ Not in cart';
        console.log(`  ${index + 1}. ${variation.color} ${variation.size}: ${cartStatus}`);
      });

      // Verify cart summary
      if (productData2.cartSummary) {
        const expectedVariations = variation2._id.toString() !== variation1._id.toString() ? 2 : 1;
        const expectedQuantity = variation2._id.toString() !== variation1._id.toString() ? 3 : 2;
        
        console.log(`\n📈 Cart Summary Verification:`);
        console.log(`  Expected variations in cart: ${expectedVariations}`);
        console.log(`  Actual variations in cart: ${productData2.cartSummary.totalVariationsInCart}`);
        console.log(`  Expected total quantity: ${expectedQuantity}`);
        console.log(`  Actual total quantity: ${productData2.cartSummary.totalQuantityInCart}`);
        
        const variationsMatch = productData2.cartSummary.totalVariationsInCart === expectedVariations;
        const quantityMatch = productData2.cartSummary.totalQuantityInCart === expectedQuantity;
        
        console.log(`  Variations count: ${variationsMatch ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`  Quantity count: ${quantityMatch ? '✅ PASS' : '❌ FAIL'}`);
      }
    }

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the test
if (require.main === module) {
  // Load environment variables
  require('dotenv').config();
  testProductCartIntegration();
}

module.exports = { testProductCartIntegration };
