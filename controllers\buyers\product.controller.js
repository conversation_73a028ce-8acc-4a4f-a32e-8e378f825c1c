const Product = require('../../models/product.model');
const { Buyer } = require('../../models/user.model');
const Category = require('../../models/category.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');
const { Types } = require('mongoose');
 const Cart = require('../../models/cart.model');

/**
 * Get product details by ID
 * @route GET /api/v1/buyers/products/:id
 * @route GET /api/v1/products/:id
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findById(req.params.id)
    .populate({
      path: 'reviews',
      options: { sort: { createdAt: -1 }, limit: 5 }, // Limit to 5 reviews
      populate: {
        path: 'user',
        select: 'name '
      }
    })
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'relatedCategories',
      select: 'name description'
    })
    .populate({
        path: 'creator',
        select: 'shopInfo.contact shopInfo.banner shopInfo.description shopInfo.logo shopInfo.name metrics'
      });
  
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Check if product is active
  if (product.status !== 'active') {
    return next(new AppError('This product is not available', 404));
  }

  // Transform category data to include path array
  if (product.category) {
    product.category._doc.pathArray = product.category.name.split(' > ');
  }
  if (product.relatedCategories?.length) {
    for (const category of product.relatedCategories) {
      if (category && category.name) {
        category._doc.pathArray = category.name.split(' > ');
      }
    }
  }

  // Add to recently viewed if authenticated
  if (req.user) {
    await Buyer.findByIdAndUpdate(
      req.user.id,
      {
        $push: {
          recentlyViewed: {
            $each: [{ product: product._id, viewedAt: new Date() }],
            $position: 0,
            $slice: 20 // Keep only the 20 most recent items
          }
        }
      },
      { new: true }
    );
  }

  // Check cart for product variations if user is authenticated
  let cartInfo = null;
  if (req.user) {
   
    const cart = await Cart.findOne({ user: req.user.id });

    if (cart && cart.products.length > 0) {
      // Find cart items that match this product
      const cartItems = cart.products.filter(item => {
        const itemProductId = item.product._id ? item.product._id.toString() : item.product.toString();
        return itemProductId === product._id.toString();
      });

      if (cartItems.length > 0) {
        cartInfo = cartItems.map(item => ({
          variationId: item.variationId,
          quantity: item.quantity,
          addedAt: item.addedAt
        }));
      }
    }
  }

  // Get related products based on category and tags
  const relatedProductsQuery = {
    _id: { $ne: product._id }, // Exclude current product
    status: 'active',
    $or: [
      { category: product.category },
      { relatedCategories: { $in: product.relatedCategories } },
      { tags: { $in: product.tags } }
    ]
  };


  // Get more products from the same shop/creator
  const shopProductsQuery = {
    _id: { $ne: product._id }, // Exclude current product
    creator: product.creator._id,
    status: 'active'
  };


  const [relatedProducts, shopProducts] = await Promise.all([
      Product.find(relatedProductsQuery)
        .select('name images basePrice variations status')
        .sort('-createdAt')
        .limit(6),
      
      Product.find(shopProductsQuery)
        .select('name images basePrice variations status')
        .sort('-createdAt')
        .limit(4)
    ]);

  
  // Extract shop information
  const shopInfo = product.creator ? {
    id: product.creator._id,
    name: product.creator.shopInfo?.name || product.creator.name,
    logo: product.creator.photo,
    metrics: {
      // Core metrics
      averageRating: product.creator.metrics?.averageRating || 0,
      qualityScore: product.creator.metrics?.qualityScore || 0,
      shippingSpeed: product.creator.metrics?.shippingSpeed || 0,

      // Business metrics
      totalProducts: product.creator.metrics?.totalProducts || 0,
      totalBales: product.creator.metrics?.totalBales || 0,
      totalSales: product.creator.metrics?.totalSales || 0,
      totalRevenue: product.creator.metrics?.totalRevenue || 0,

      // Social metrics
      followers: product.creator.metrics?.followers || 0
    },
  } : null;

  // Process variations to include current prices, stock information, and cart status using virtuals
  const processedVariations = product.variations.map(variation => {
    const variationObj = variation.toObject();

    // Get stock status information
    const stockStatus = {
      quantity: variation.quantity,
      isLowStock: variation.isLowStock,
      isOutOfStock: variation.isOutOfStock,
      lastRestocked: variation.lastRestocked
    };

    // Get last restock information if available
    const lastRestockInfo = variation.lastRestockInfo;
    if (lastRestockInfo) {
      stockStatus.lastRestockInfo = {
        date: lastRestockInfo.date,
        amount: lastRestockInfo.amount
      };
    }

    // Check if this variation is in the user's cart
    let inCart = null;
    if (cartInfo) {
      const cartItem = cartInfo.find(item => item.variationId === variation._id.toString());
      if (cartItem) {
        inCart = {
          quantity: cartItem.quantity,
          addedAt: cartItem.addedAt
        };
      }
    }

    return {
      ...variationObj,
      currentPrice: variation.currentPrice,
      hasDiscount: variation.hasDiscount,
      discountSource: variation.discountSource,
      discountPercentage: variation.discountPercentage,
      discountEndDate: variation.discountEndDate,
      stockStatus,
      inCart
    };
  });

  // Use the formattedPriceRange virtual for price range information
  const priceRange = product.formattedPriceRange;

  // Get active promotion info using the bestPromotion virtual
  const activePromotion = product.hasActivePromotion ? {
    id: product.bestPromotion._id,
    discountType: product.bestPromotion.discountType,
    discountValue: product.bestPromotion.discountValue,
    startDate: product.bestPromotion.startDate,
    endDate: product.bestPromotion.endDate,
    remainingStock: product.bestPromotion.promoStock
  } : null;

  // Calculate review statistics
  let reviewStats = {
    averageRating: product.ratingsAverage || 0,
    totalReviews: product.ratingsQuantity || 0,
    reviews: product.reviews || []
  };

  // Format related products using virtuals
  const formattedRelatedProducts = relatedProducts.map(product => {
    const productObj = product.toObject();

    return {
      _id: productObj._id,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: product.maxDiscountPercentage
    };
  });

  // Format shop products using virtuals
  const formattedShopProducts = shopProducts.map(product => {
    const productObj = product.toObject();

    return {
      _id: productObj._id,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: product.maxDiscountPercentage
    };
  });

  // Format the response
  const formattedProduct = {
    ...product._doc,
    variations: processedVariations,
    priceRange,
    activePromotion,
    reviewStats,
    shopInfo,
    cartSummary: cartInfo ? {
      totalVariationsInCart: cartInfo.length,
      totalQuantityInCart: cartInfo.reduce((sum, item) => sum + item.quantity, 0)
    } : null
  };

  res.status(200).json({
    status: 'success',
    data: {
      product: formattedProduct,
      relatedProducts: formattedRelatedProducts,
      shopProducts: formattedShopProducts
    }
  });
});

/**
 * Get products and bales based on buyer preferences with fallback to general filtering
 * @route GET /api/v1/buyers/products/feed
 * @route GET /api/v1/products
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getProductFeed = catchAsync(async (req, res, next) => {
  const allowedFilters = [
    'category',
    'brands',
    'sizes',
    'colors',
    'gender',
    'countries',
    'conditions',
    'minPrice',
    'maxPrice',
    'type',
    'discountRange'
  ];

  const filterQuery = {};
  allowedFilters.forEach(field => {
    if (req.query[field] !== undefined) {
      filterQuery[field] = req.query[field];
    }
  });

  const toArray = val => Array.isArray(val) ? val : [val];
  let queryObj = { status: 'active' };
  const orFilters = [];

  // Type filtering
  if (filterQuery.type && ['product', 'bale'].includes(filterQuery.type)) {
    queryObj.type = filterQuery.type;
  } else {
    queryObj.type = { $in: ['product', 'bale'] };
  }

  if (filterQuery.category) {
    const rawCategories = toArray(filterQuery.category)
      .flatMap(c => c.split(','))
      .map(c => c.trim())
      .filter(Boolean);

    let categories = [];
    try {
      categories = rawCategories.map(id => new Types.ObjectId(id));
    } catch (err) {
      return next(new AppError('Invalid category ID(s)', 400));
    }

    orFilters.push({ category: { $in: categories } });
    orFilters.push({ relatedCategories: { $in: categories } });
  }

  if (filterQuery.brands) {
    const rawBrands = toArray(filterQuery.brands)
      .flatMap(b => b.split(','))
      .map(b => b.trim())
      .filter(Boolean);

    orFilters.push({ brand: { $in: rawBrands } });
  }

  if (filterQuery.sizes) {
    const rawSizes = toArray(filterQuery.sizes)
      .flatMap(s => s.split(','))
      .map(s => s.trim())
      .filter(Boolean);

    orFilters.push({ 'variations.size': { $in: rawSizes } });
  }

  if (filterQuery.colors) {
    const rawColors = toArray(filterQuery.colors)
      .flatMap(c => c.split(','))
      .map(c => c.trim())
      .filter(Boolean);

    orFilters.push({ 'variations.color': { $in: rawColors } });
  }

  if (filterQuery.gender) {
    const genders = toArray(filterQuery.gender)
      .flatMap(g => g.split(','))
      .map(g => g.trim())
      .filter(Boolean);

    orFilters.push({ gender: { $in: genders } });
  }

  if (filterQuery.countries) {
    const countries = toArray(filterQuery.countries)
      .flatMap(c => c.split(','))
      .map(c => c.trim())
      .filter(Boolean);

    orFilters.push({ country: { $in: countries } });
  }

  if (filterQuery.conditions) {
    const conditions = toArray(filterQuery.conditions)
      .flatMap(c => c.split(','))
      .map(c => c.trim())
      .filter(Boolean);

    orFilters.push({ condition: { $in: conditions } });
  }

  if (filterQuery.minPrice || filterQuery.maxPrice) {
    const priceFilter = {};
    if (filterQuery.minPrice) priceFilter.$gte = Number(filterQuery.minPrice);
    if (filterQuery.maxPrice) priceFilter.$lte = Number(filterQuery.maxPrice);
    orFilters.push({ basePrice: priceFilter });
  }

  if (filterQuery.discountRange) {
    const discountLimit = Number(filterQuery.discountRange);
    if (!isNaN(discountLimit)) {
      orFilters.push({
        $expr: {
          $gte: [
            {
              $max: {
                $map: {
                  input: {
                    $filter: {
                      input: '$variations',
                      as: 'v',
                      cond: {
                        $and: [
                          { $gt: ['$$v.price', 0] },
                          { $lte: ['$$NOW', '$$v.saleEndDate'] },
                          { $gte: ['$$NOW', '$$v.saleStartDate'] },
                          { $gt: ['$$v.price', '$$v.salePrice'] }
                        ]
                      }
                    }
                  },
                  as: 'v',
                  in: {
                    $multiply: [
                      {
                        $divide: [
                          { $subtract: ['$$v.price', '$$v.salePrice'] },
                          '$$v.price'
                        ]
                      },
                      100
                    ]
                  }
                }
              }
            },
            discountLimit
          ]
        }
      });
    }
  }

  if (orFilters.length > 0) {
    queryObj = {
      $and: [
        { status: 'active' },
        { type: queryObj.type },
        { $or: orFilters }
      ]
    };
  }

  let query = Product.find(queryObj);
  // console.log("Query ",query);
  // Text search
  if (req.query.search) {
    const regex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: regex },
        { description: regex },
        { brand: regex },
        { tags: regex },
        { 'specifications.mainMaterial': regex }
      ]
    });
  }

  const sortOptions = {
    'price-asc': 'basePrice',
    'price-desc': '-basePrice',
    'name-asc': 'name',
    'name-desc': '-name',
    'rating-desc': '-ratingsAverage',
    'newest': '-createdAt',
    'popular': '-sold'
  };
  const sortBy = sortOptions[req.query.sort] || '-createdAt';
  query = query.sort(sortBy);

  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  const page = Number(req.query.page) || 1;
  const limit = Number(req.query.limit) || 20;
  const skip = (page - 1) * limit;
  query = query.skip(skip).limit(limit);

  const [items, total] = await Promise.all([
    query
      .populate({ path: 'category', select: 'name description' })
      .populate({ path: 'creator', select: 'name photo' }),
    Product.countDocuments(queryObj)
  ]);

  const now = new Date();
  const formattedItems = items.map(item => {
    const obj = item.toObject();
    let maxDiscountPercentage = 0;

    if (obj.variations?.length > 0) {
      const sales = obj.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );
      for (const v of sales) {
        const discount = ((v.price - v.salePrice) / v.price) * 100;
        maxDiscountPercentage = Math.max(maxDiscountPercentage, discount);
      }
    }

    const base = {
      _id: obj._id,
      type: obj.type,
      name: obj.name,
      images: obj.images,
      basePrice: obj.basePrice,
      status: obj.status,
      maxDiscountPercentage,
      ratingsAverage: obj.ratingsAverage || 0,
      ratingsQuantity: obj.ratingsQuantity || 0,
      formattedPriceRange: obj.formattedPriceRange,
      hasAnyDiscount: obj.hasAnyDiscount,
      totalItemsLeft: obj.totalStock
    };

    return obj.type === 'product'
      ? { ...base, availableColors: obj.availableColors, availableSizes: obj.availableSizes }
      : {
          ...base,
          country: obj.country,
          totalItems: obj.totalItems,
          condition: obj.condition,
          availableIdentifiers: obj.availableIdentifiers
        };
  });

  formattedItems.sort((a, b) => b.maxDiscountPercentage - a.maxDiscountPercentage);

  res.status(200).json({
    status: 'success',
    results: formattedItems.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      items: formattedItems,
      productCount: formattedItems.filter(i => i.type === 'product').length,
      baleCount: formattedItems.filter(i => i.type === 'bale').length
    }
  });
});




exports.getFlashySales = catchAsync(async (req, res, next) => {
  const now = new Date();

  // Calculate the start and end of the current week
  const getWeekBounds = (date) => {
    const currentDate = new Date(date);
    const day = currentDate.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - day + (day === 0 ? -6 : 1));
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return { startOfWeek, endOfWeek };
  };

  let { startOfWeek, endOfWeek } = getWeekBounds(now);

  // Query products and bales with active sales within the current week
  const productQuery = {
    status: 'active',
    variations: {
      $elemMatch: {
        salePrice: { $exists: true },
        saleStartDate: { $lte: endOfWeek },
        saleEndDate: { $gte: startOfWeek }
      }
    }
  };

  const baleQuery = {
    status: 'active',
    variations: {
      $elemMatch: {
        salePrice: { $exists: true },
        saleStartDate: { $lte: endOfWeek },
        saleEndDate: { $gte: startOfWeek }
      }
    }
  };

  // Find products and bales with active sales
  const products = await Product.find(productQuery);
  const bales = await Product.find({ ...baleQuery, type: 'bale' });

  // Helper function to calculate hours left
  const calculateHoursLeft = (endDate) => {
    const timeLeftMs = new Date(endDate) - now;
    const hoursLeft = Math.floor(timeLeftMs / (1000 * 60 * 60));
    return hoursLeft;
  };

  // Process products and bales
  const processItems = (items, type) => {
    return items.map(item => {
      const salesVariations = item.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        v.saleStartDate <= endOfWeek && v.saleEndDate >= startOfWeek
      );



      const earliestEndDate = new Date(Math.min(...salesVariations.map(v => v.saleEndDate.getTime())));
      const hoursLeft = calculateHoursLeft(earliestEndDate);

      const isEndingSoon = hoursLeft <= 48;  // Mark as ending soon if within 48 hours

      // Use virtuals for variations info based on type
      let variationsInfo = {};
      if (type === 'product') {
        variationsInfo = {
          availableColors: item.availableColors,
          availableSizes: item.availableSizes
        };
      } else if (type === 'bale') {
        variationsInfo = {
          availableIdentifiers: item.availableIdentifiers
        };
      }

      return {
        _id: item._id,
        type,
        name: item.name,
        images: item.images,
        basePrice: item.basePrice,
        maxDiscountPercentage: item.maxDiscountPercentage, // Use virtual
        saleEndDate: earliestEndDate,
        isEndingSoon,
        hoursLeft,
        ...variationsInfo,
        totalItemsLeft: item.totalStock // Use virtual
      };
    });
  };

  // Process both products and bales
  const processedProducts = processItems(products, 'product');
  const processedBales = processItems(bales, 'bale');

  // Combine and sort items by urgency (ending soon first)
  const allItems = [...processedProducts, ...processedBales];
  allItems.sort((a, b) => {
    if (a.isEndingSoon && !b.isEndingSoon) return -1;
    if (!a.isEndingSoon && b.isEndingSoon) return 1;
    return a.hoursLeft - b.hoursLeft; // If both are ending soon, sort by hours left
  });

  // Return the final response with added week start and end date and counts
  res.status(200).json({
    status: 'success',
    weekStartDate: startOfWeek,
    weekEndDate: endOfWeek,
    productCount: processedProducts.length,
    baleCount: processedBales.length,
    results: allItems.length,
    data: allItems
  });
});


/**
 * Get products for a specific category including all its subcategories using path-based matching
 * @route GET /api/v1/buyers/products/category/:id
 * @route GET /api/v1/products/category/:id
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getProductsByCategory = catchAsync(async (req, res, next) => {
  const categoryId = req.params.id;

  // Validate category exists
  const category = await Category.findById(categoryId);
  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  // Get subcategory details for display
  const subcategories = await Category.find({ parent: categoryId })
    .select('name description image');

  // Get the category path
  const categoryPath = category.name;

  // Get pagination parameters
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Build query for products in this category and all its subcategories
  // Find all categories that have this category's path as a prefix
  const allCategoriesInPath = await Category.find({
    name: { $regex: `^${categoryPath}` }
  }).select('_id');

  const categoryIds = allCategoriesInPath.map(cat => cat._id);

  // Build query for products in these categories
  const query = {
    status: 'active',
    category: { $in: categoryIds }
  };

  // Apply filters if provided
  if (req.query.minPrice || req.query.maxPrice) {
    const priceFilter = {};
    if (req.query.minPrice) priceFilter.$gte = parseFloat(req.query.minPrice);
    if (req.query.maxPrice) priceFilter.$lte = parseFloat(req.query.maxPrice);
    query.basePrice = priceFilter;
  }

  // Filter by color
  if (req.query.color) {
    query['variations.color'] = req.query.color;
  }

  // Filter by size
  if (req.query.size) {
    query['variations.size'] = req.query.size;
  }

  // Count total products matching the query
  const total = await Product.countDocuments(query);

  // Get sorting parameter
  let sortBy = '-createdAt';
  if (req.query.sort) {
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      case 'newest':
        sortBy = '-createdAt';
        break;
      case 'popular':
        sortBy = '-sold';
        break;
      default:
        sortBy = req.query.sort.split(',').join(' ');
    }
  }

  // Get products with pagination and sorting
  const products = await Product.find(query)
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'creator',
      select: 'name photo shopInfo'
    })
    .sort(sortBy)
    .skip(skip)
    .limit(limit);

  // Calculate discount percentages and format products with essential details
  const now = new Date();
  const productsWithDiscountInfo = products.map(product => {
    const productObj = product.toObject();

    // Find sale end date for display
    let latestEndDate = null;
    let daysLeft = 0;

    if (product.variations && product.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = product.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Find the latest end date
      if (salesVariations.length > 0) {
        latestEndDate = new Date(Math.max(...salesVariations.map(v => v.saleEndDate.getTime())));
        // Calculate days left
        daysLeft = Math.ceil((latestEndDate - now) / (1000 * 60 * 60 * 24));
      }
    }

    // Use virtuals for variations info based on type
    let variationsInfo = {};
    if (product.type === 'product') {
      variationsInfo = {
        availableColors: product.availableColors,
        availableSizes: product.availableSizes
      };
    } else if (product.type === 'bale') {
      variationsInfo = {
        availableIdentifiers: product.availableIdentifiers
      };
    }

    // Create simplified product object with essential details
    return {
      _id: productObj._id,
      type: productObj.type,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: product.maxDiscountPercentage, // Use virtual
      saleEndDate: latestEndDate,
      daysLeft: daysLeft,
      ratingsAverage: productObj.ratingsAverage || 0,
      ratingsQuantity: productObj.ratingsQuantity || 0,
      ...variationsInfo,
      totalItemsLeft: product.totalStock // Use virtual
    };
  });

  // Transform category data to include path array and subcategory information
  const categoryData = {
    _id: category._id,
    name: category.name,
    description: category.description,
    pathArray: category.name.split(' > '),
    subcategories: subcategories,
    includesSubcategoryProducts: categoryIds.length > 1 // More than just the main category
  };

  res.status(200).json({
    status: 'success',
    results: products.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      category: categoryData,
      products: productsWithDiscountInfo
    }
  });
});

/**
 * Get featured products
 * @route GET /api/v1/buyers/products/featured
 * @route GET /api/v1/products/featured
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getFeaturedProducts = catchAsync(async (req, res, next) => {
  // Get pagination parameters
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 10;
  const skip = (page - 1) * limit;

  // Build query for featured products
  // Featured products are those marked as featured by admin
  const query = {
    status: 'active',
    featured: true
  };

  // Count total featured products
  const total = await Product.countDocuments(query);

  // Get featured products with pagination
  const products = await Product.find(query)
    .sort('-createdAt')
    .skip(skip)
    .limit(limit);

  // Calculate discount percentages and format products
  const now = new Date();
  const formattedProducts = products.map(product => {
    const productObj = product.toObject();

    // Find sale end date for display
    let latestEndDate = null;
    let daysLeft = 0;

    if (product.variations && product.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = product.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Find the latest end date
      if (salesVariations.length > 0) {
        latestEndDate = new Date(Math.max(...salesVariations.map(v => v.saleEndDate.getTime())));
        // Calculate days left
        daysLeft = Math.ceil((latestEndDate - now) / (1000 * 60 * 60 * 24));
      }
    }

    // Use virtuals for variations info based on type
    let variationsInfo = {};
    if (product.type === 'product') {
      variationsInfo = {
        availableColors: product.availableColors,
        availableSizes: product.availableSizes
      };
    } else if (product.type === 'bale') {
      variationsInfo = {
        availableIdentifiers: product.availableIdentifiers
      };
    }

    // Create simplified product object with essential details
    return {
      _id: productObj._id,
      type: productObj.type,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: product.maxDiscountPercentage, // Use virtual
      saleEndDate: latestEndDate,
      daysLeft: daysLeft,
      ratingsAverage: productObj.ratingsAverage || 0,
      ratingsQuantity: productObj.ratingsQuantity || 0,
      featured: productObj.featured || false,
      ...variationsInfo,
      totalItemsLeft: product.totalStock // Use virtual
    };
  });

  res.status(200).json({
    status: 'success',
    results: formattedProducts.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      products: formattedProducts
    }
  });
});



/**
 * Get all discounted products sorted by highest discount
 * @route GET /api/v1/buyers/products/discounted
 * @route GET /api/v1/products/discounted
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getDiscountedProducts = catchAsync(async (req, res, next) => {
  const now = new Date();

  // Get pagination parameters
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Find products with active discounts
  const query = {
    status: 'active',
    variations: {
      $elemMatch: {
        salePrice: { $exists: true },
        saleStartDate: { $lte: now },
        saleEndDate: { $gte: now }
      }
    }
  };

  // Calculate discount percentage for each product using aggregation
  const productsWithDiscount = await Product.aggregate([
    {
      $match: query
    },
    {
      $unwind: '$variations'
    },
    {
      $match: {
        'variations.salePrice': { $exists: true },
        'variations.saleStartDate': { $lte: now },
        'variations.saleEndDate': { $gte: now }
      }
    },
    {
      $addFields: {
        discountPercentage: {
          $multiply: [
            {
              $divide: [
                { $subtract: ['$variations.price', '$variations.salePrice'] },
                '$variations.price'
              ]
            },
            100
          ]
        }
      }
    },
    {
      $group: {
        _id: '$_id',
        name: { $first: '$name' },
        images: { $first: '$images' },
        basePrice: { $first: '$basePrice' },
        status: { $first: '$status' },
        category: { $first: '$category' },
        creator: { $first: '$creator' },
        ratingsAverage: { $first: '$ratingsAverage' },
        ratingsQuantity: { $first: '$ratingsQuantity' },
        maxDiscountPercentage: { $max: '$discountPercentage' }
      }
    },
    {
      $sort: { maxDiscountPercentage: -1 }
    },
    {
      $skip: skip
    },
    {
      $limit: limit
    }
  ]);

  // Count total discounted products
  const totalCount = await Product.aggregate([
    {
      $match: query
    },
    {
      $count: 'total'
    }
  ]);

  const total = totalCount.length > 0 ? totalCount[0].total : 0;

  // Get the full product details to calculate days left
  const productIds = productsWithDiscount.map(p => p._id);
  const fullProducts = await Product.find({ _id: { $in: productIds } });

  // Format products with time left information
  const formattedProducts = productsWithDiscount.map(product => {
    // Find the full product to get variation details
    const fullProduct = fullProducts.find(p => p._id.toString() === product._id.toString());

    // Calculate days left
    let latestEndDate = null;
    let daysLeft = 0;

    if (fullProduct && fullProduct.variations && fullProduct.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = fullProduct.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Find the latest end date
      if (salesVariations.length > 0) {
        latestEndDate = new Date(Math.max(...salesVariations.map(v => v.saleEndDate.getTime())));
        // Calculate days left
        daysLeft = Math.ceil((latestEndDate - now) / (1000 * 60 * 60 * 24));
      }
    }

    // Use virtuals for variations info based on type
    let variationsInfo = {};
    if (fullProduct.type === 'product') {
      variationsInfo = {
        availableColors: fullProduct.availableColors,
        availableSizes: fullProduct.availableSizes
      };
    } else if (fullProduct.type === 'bale') {
      variationsInfo = {
        availableIdentifiers: fullProduct.availableIdentifiers
      };
    }

    return {
      _id: product._id,
      type: fullProduct.type,
      name: product.name,
      images: product.images,
      basePrice: product.basePrice,
      status: product.status,
      maxDiscountPercentage: fullProduct.maxDiscountPercentage, // Use virtual
      saleEndDate: latestEndDate,
      daysLeft: daysLeft,
      ratingsAverage: product.ratingsAverage || 0,
      ratingsQuantity: product.ratingsQuantity || 0,
      ...variationsInfo,
      totalItemsLeft: fullProduct.totalStock // Use virtual
    };
  });

  res.status(200).json({
    status: 'success',
    results: formattedProducts.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      products: formattedProducts
    }
  });
});

/**
 * Get products grouped by multiple categories with highest discount first
 * @route GET /api/v1/buyers/products/by-categories
 * @route GET /api/v1/products/by-categories
 * @access Public (works for both authenticated and unauthenticated users)
 */
/**
 * Get filter parameters for products and bales
 * @route GET /api/v1/buyers/products/filter-params
 * @route GET /api/v1/products/filter-params
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getFilterParams = catchAsync(async (req, res, next) => {
  // Base filter for active products only
  const activeProductFilter = { status: 'active' };

  // Use a single aggregation pipeline to get all filter data efficiently
  const filterData = await Product.aggregate([
    { $match: activeProductFilter },
    {
      $facet: {
        // Get categories that have active products
        categories: [
          { $group: { _id: '$category' } },
          { $lookup: { from: 'categories', localField: '_id', foreignField: '_id', as: 'categoryData' } },
          { $unwind: '$categoryData' },
          { $replaceRoot: { newRoot: '$categoryData' } },
          { $project: { name: 1, description: 1, parent: 1 } }
        ],
        // Get brands from active products
        brands: [
          { $group: { _id: '$brand' } },
          { $match: { _id: { $ne: null } } },
          { $sort: { _id: 1 } }
        ],
        // Get countries from active bales
        countries: [
          { $match: { type: 'bale' } },
          { $group: { _id: '$country' } },
          { $match: { _id: { $ne: null } } },
          { $sort: { _id: 1 } }
        ],
        // Get conditions from active bales
        conditions: [
          { $match: { type: 'bale' } },
          { $group: { _id: '$condition' } },
          { $match: { _id: { $ne: null } } },
          { $sort: { _id: 1 } }
        ],
        // Get genders from active products
        genders: [
          { $match: { type: 'product' } },
          { $group: { _id: '$gender' } },
          { $match: { _id: { $ne: null } } },
          { $sort: { _id: 1 } }
        ],
        // Get price range for all active products
        priceRange: [
          {
            $group: {
              _id: null,
              minPrice: { $min: '$basePrice' },
              maxPrice: { $max: '$basePrice' }
            }
          }
        ],
        // Get sizes from active product variations
        sizes: [
          { $match: { type: 'product' } },
          { $unwind: '$variations' },
          { $match: { 'variations.size': { $ne: null } } },
          { $group: { _id: '$variations.size' } },
          { $sort: { _id: 1 } }
        ],
        // Get identifiers from active bale variations
        identifiers: [
          { $match: { type: 'bale' } },
          { $unwind: '$variations' },
          { $match: { 'variations.identifier': { $ne: null } } },
          { $group: { _id: '$variations.identifier' } },
          { $sort: { _id: 1 } }
        ]
      }
    }
  ]);

  const data = filterData[0];

  // Build category tree from categories that have products
  const categoryMap = {};
  const rootCategories = [];

  // Create category map
  data.categories.forEach(category => {
    categoryMap[category._id] = {
      ...category,
      children: []
    };
  });

  // Build hierarchy
  data.categories.forEach(category => {
    if (category.parent) {
      // This is a child category
      if (categoryMap[category.parent]) {
        categoryMap[category.parent].children.push(categoryMap[category._id]);
      }
    } else {
      // This is a root category
      rootCategories.push(categoryMap[category._id]);
    }
  });

  const categoryTree = rootCategories;

  // Extract values and filter out nulls
  const brands = data.brands.map(b => b._id);
  const countries = data.countries.map(c => c._id);
  const conditions = data.conditions.map(c => c._id);
  const genders = data.genders.map(g => g._id);
  const sizes = data.sizes.map(s => s._id);
  const identifiers = data.identifiers.map(i => i._id);

  // Get price range
  const priceRange = data.priceRange.length > 0
    ? { min: data.priceRange[0].minPrice, max: data.priceRange[0].maxPrice }
    : { min: 0, max: 0 };

  // Define sort options
  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'price-low-high', label: 'Price: Low to High' },
    { value: 'price-high-low', label: 'Price: High to Low' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'popular', label: 'Most Popular' }
  ];

  res.status(200).json({
    status: 'success',
    data: {
      categories: categoryTree,
      brands,
      countries,
      conditions,
      sizes,
      identifiers,
      genders,
      priceRange,
      sortOptions
    }
  });
});

exports.getProductsByCategories = catchAsync(async (req, res, next) => {
  const now = new Date();
  const productsPerCategory = req.query.limit * 1 || 6;

  // Category pagination
  const page = req.query.page * 1 || 1;
  const categoriesPerPage = req.query.categoriesPerPage * 1 || 10;
  const skip = (page - 1) * categoriesPerPage;

  // Get all active categories that have products
  const categoriesWithProducts = await Product.aggregate([
    {
      $match: { status: 'active' }
    },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 }
      }
    },
    {
      $match: {
        count: { $gt: 0 }
      }
    }
  ]);

  // Get total count of categories with products
  const totalCategories = categoriesWithProducts.length;

  // Get category IDs
  const allCategoryIds = categoriesWithProducts.map(c => c._id);

  // Calculate discount percentage for each product with variations on sale
  const productsWithDiscount = await Product.aggregate([
    {
      $match: {
        status: 'active',
        category: { $in: allCategoryIds },
        variations: {
          $elemMatch: {
            salePrice: { $exists: true },
            saleStartDate: { $lte: now },
            saleEndDate: { $gte: now }
          }
        }
      }
    },
    {
      $unwind: '$variations'
    },
    {
      $match: {
        'variations.salePrice': { $exists: true },
        'variations.saleStartDate': { $lte: now },
        'variations.saleEndDate': { $gte: now }
      }
    },
    {
      $addFields: {
        discountPercentage: {
          $multiply: [
            {
              $divide: [
                { $subtract: ['$variations.price', '$variations.salePrice'] },
                '$variations.price'
              ]
            },
            100
          ]
        }
      }
    },
    {
      $group: {
        _id: '$_id',
        name: { $first: '$name' },
        brand: { $first: '$brand' },
        basePrice: { $first: '$basePrice' },
        images: { $first: '$images' },
        category: { $first: '$category' },
        creator: { $first: '$creator' },
        maxDiscountPercentage: { $max: '$discountPercentage' }
      }
    }
  ]);

  // Create a map of product ID to discount percentage
  const discountMap = {};
  productsWithDiscount.forEach(p => {
    discountMap[p._id.toString()] = p.maxDiscountPercentage;
  });

  // Get category discount info to sort categories
  const categoryDiscountMap = {};
  for (const product of productsWithDiscount) {
    const categoryId = product.category.toString();
    const discount = product.maxDiscountPercentage || 0;

    if (!categoryDiscountMap[categoryId] || categoryDiscountMap[categoryId] < discount) {
      categoryDiscountMap[categoryId] = discount;
    }
  }

  // Sort category IDs by max discount percentage
  const sortedCategoryIds = [...allCategoryIds].sort((a, b) => {
    const discountA = categoryDiscountMap[a.toString()] || 0;
    const discountB = categoryDiscountMap[b.toString()] || 0;
    return discountB - discountA;
  });

  // Apply pagination to category IDs
  const paginatedCategoryIds = sortedCategoryIds.slice(skip, skip + categoriesPerPage);

  // Get Category details including subcategories
  const categoryDetails = await Promise.all(
    paginatedCategoryIds.map(async (categoryId) => {
      const category = await Category.findById(categoryId)
        .select('name description parent')
        .populate({
          path: 'subcategories',
          select: 'name description image'
        });

      return category;
    })
  );

  // Create a map of category details
  const categoryDetailsMap = {};
  categoryDetails.forEach(category => {
    if (category) {
      categoryDetailsMap[category._id.toString()] = category;
    }
  });

  // Get products for each category
  const categoryResults = [];
  for (const categoryId of paginatedCategoryIds) {
    // Get products for this category
    const products = await Product.find({
      status: 'active',
      category: categoryId
    })
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'creator',
      select: 'name photo shopInfo'
    })
    .limit(productsPerCategory * 2); // Get more than we need to ensure we have enough after filtering

    // Skip if no products found
    if (products.length === 0) continue;

    // Add discount percentage and format products with essential details
    const productsWithDiscountInfo = products.map(product => {
      const productObj = product.toObject();
      const maxDiscountPercentage = discountMap[product._id.toString()] || 0;

      // Use virtuals for variations info based on type
      let variationsInfo = {};
      if (product.type === 'product') {
        variationsInfo = {
          availableColors: product.availableColors,
          availableSizes: product.availableSizes
        };
      } else if (product.type === 'bale') {
        variationsInfo = {
          availableIdentifiers: product.availableIdentifiers
        };
      }

      // Create simplified product object with essential details
      return {
        _id: productObj._id,
        type: productObj.type,
        name: productObj.name,
        images: productObj.images,
        basePrice: productObj.basePrice,
        status: productObj.status,
        maxDiscountPercentage: Math.round(maxDiscountPercentage),
        ratingsAverage: productObj.ratingsAverage || 0,
        ratingsQuantity: productObj.ratingsQuantity || 0,
        ...variationsInfo,
        totalItemsLeft: product.totalStock // Use virtual
      };
    });

    // Sort by discount percentage (highest first)
    productsWithDiscountInfo.sort((a, b) => b.maxDiscountPercentage - a.maxDiscountPercentage);

    // Take only the requested number of products
    const limitedProducts = productsWithDiscountInfo.slice(0, productsPerCategory);

    // Calculate max discount percentage for this category
    const maxDiscountPercentage = limitedProducts.length > 0 ?
      Math.max(...limitedProducts.map(p => p.maxDiscountPercentage || 0)) : 0;

    // Get category details including subcategories
    const categoryDetail = categoryDetailsMap[categoryId.toString()];

    if (categoryDetail) {
      const categoryName = categoryDetail.name;
      const pathArray = categoryName ? categoryName.split(' > ') : [];

      // Get subcategories
      const subcategories = categoryDetail.subcategories || [];

      // Add category info and products to results
      categoryResults.push({
        category: {
          _id: categoryId,
          name: categoryName,
          description: categoryDetail.description,
          pathArray,
          maxDiscountPercentage,
          subcategories: subcategories.map(sub => ({
            _id: sub._id,
            name: sub.name,
            description: sub.description,
            image: sub.image
          }))
        },
        products: limitedProducts
      });
    }
  }

  res.status(200).json({
    status: 'success',
    results: categoryResults.length,
    total: totalCategories,
    page,
    limit: categoriesPerPage,
    totalPages: Math.ceil(totalCategories / categoriesPerPage),
    data: {
      categories: categoryResults
    }
  });
});





