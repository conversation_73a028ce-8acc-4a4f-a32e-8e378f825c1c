const Product = require('../models/product.model');
const { Creator } = require('../models/user.model');
const Category = require('../models/category.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Helper function to parse comma-separated values
 */
const parseCommaSeparated = (value) => {
  if (!value) return [];
  if (Array.isArray(value)) return value;
  return value.split(',').map(item => item.trim()).filter(item => item);
};

/**
 * Search products, bales, and shops
 * @route GET /api/v1/search
 * @access Public
 */
const buildSearchFilters = (query, type, searchRegex) => {
  const andConditions = [{ status: 'active' }, { type }];
  const orFilters = [];

  const parse = (val) =>
    Array.isArray(val)
      ? val.flatMap((v) => v.split(',').map((s) => s.trim()))
      : String(val).split(',').map((s) => s.trim());

  const pushFilter = (field, value, dbField = field) => {
    if (!value) return;
    const parsed = parse(value);
    if (parsed.length) {
      orFilters.push({ [dbField]: { $in: parsed } });
    }
  };

  // Categories and relatedCategories
  if (query.category) {
    try {
      const categories = parse(query.category).map((id) => new Types.ObjectId(id));
      orFilters.push({ category: { $in: categories } });
      orFilters.push({ relatedCategories: { $in: categories } });
    } catch {
      throw new AppError('Invalid category ID(s)', 400);
    }
  }

  // Price Range
  if (query.minPrice || query.maxPrice) {
    const price = {};
    if (query.minPrice) price.$gte = parseFloat(query.minPrice);
    if (query.maxPrice) price.$lte = parseFloat(query.maxPrice);
    orFilters.push({ basePrice: price });
  }

  // Discount Range
  if (query.discountRange) {
    const discountLimit = Number(query.discountRange);
    if (!isNaN(discountLimit)) {
      orFilters.push({
        $expr: {
          $gte: [
            {
              $max: {
                $map: {
                  input: {
                    $filter: {
                      input: '$variations',
                      as: 'v',
                      cond: {
                        $and: [
                          { $gt: ['$$v.price', 0] },
                          { $lte: ['$$NOW', '$$v.saleEndDate'] },
                          { $gte: ['$$NOW', '$$v.saleStartDate'] },
                          { $gt: ['$$v.price', '$$v.salePrice'] }
                        ]
                      }
                    }
                  },
                  as: 'v',
                  in: {
                    $multiply: [
                      {
                        $divide: [
                          { $subtract: ['$$v.price', '$$v.salePrice'] },
                          '$$v.price'
                        ]
                      },
                      100
                    ]
                  }
                }
              }
            },
            discountLimit
          ]
        }
      });
    }
  }

  // Common filters
  pushFilter('colors', query.colors, 'variations.color');
  pushFilter('sizes', query.sizes, 'variations.size');

  // Product-specific
  if (type === 'product') {
    pushFilter('brands', query.brands, 'brand');
    pushFilter('gender', query.gender);
  }

  // Bale-specific
  if (type === 'bale') {
    pushFilter('countries', query.countries, 'country');
    pushFilter('conditions', query.conditions, 'condition');
  }

  // Creator
  if (query.creator) {
    orFilters.push({ creator: query.creator });
  }

  // Text search
  const textSearch = {
    $or: [
      { name: searchRegex },
      { description: searchRegex },
      ...(type === 'product'
        ? [
            { brand: searchRegex },
            { tags: searchRegex },
            { 'specifications.mainMaterial': searchRegex }
          ]
        : [
            { country: searchRegex },
            { tags: searchRegex }
          ])
    ]
  };
  orFilters.push(textSearch);

  if (orFilters.length > 0) {
    andConditions.push({ $or: orFilters });
  }

  return { $and: andConditions };
};



exports.search = catchAsync(async (req, res, next) => {
  const query = req.query.q;
  if (!query) return next(new AppError('Please provide a search query', 400));

  const type = req.query.type || 'all';
  const searchRegex = new RegExp(query, 'i');

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  let sortBy = '-createdAt';
  const sortMap = {
    'price-asc': 'basePrice',
    'price-desc': '-basePrice',
    'name-asc': 'name',
    'name-desc': '-name',
    'rating-desc': '-ratingsAverage',
    'newest': '-createdAt',
    'popular': '-sold'
  };
  sortBy = sortMap[req.query.sort] || sortBy;

  const maxPerType = Math.floor(limit / 3);

  const queries = [];

  const fetchProducts = ['all', 'products'].includes(type);
  const fetchBales = ['all', 'bales'].includes(type);
  const fetchShops = ['all', 'shops'].includes(type);

  if (fetchProducts) {
    const filters = buildSearchFilters(req.query, 'product', searchRegex);
    queries.push(
      Product.find(filters)
        .populate('creator', 'shopInfo.name shopInfo.logo')
        .sort(sortBy)
        .skip(type === 'all' ? 0 : skip)
        .limit(type === 'all' ? maxPerType : limit)
        .select('name brand basePrice formattedPriceRange images ratingsAverage ratingsQuantity gender variations type creator status')

    );
  } else {
    queries.push(Promise.resolve([]));
  }

  if (fetchBales) {
    const filters = buildSearchFilters(req.query, 'bale', searchRegex);
    queries.push(
      Product.find(filters)
        .populate('creator', 'shopInfo.name shopInfo.logo')
        .sort(sortBy)
        .skip(type === 'all' ? 0 : skip)
        .limit(type === 'all' ? maxPerType : limit)
        .select('name basePrice formattedPriceRange images ratingsAverage ratingsQuantity country condition variations type creator totalItems weight status')

    );
  } else {
    queries.push(Promise.resolve([]));
  }

  if (fetchShops) {
    const shopQuery = {
      $or: [
        { name: searchRegex },
        { 'shopInfo.name': searchRegex },
        { 'shopInfo.description': searchRegex },
        { 'businessInfo.businessName': searchRegex }
      ],
      verificationStatus: 'verified',
      onboardingStatus: 'completed'
    };

    queries.push(
      Creator.find(shopQuery)
        .sort('-metrics.followers')
        .skip(type === 'all' ? 0 : skip)
        .limit(type === 'all' ? maxPerType : limit)
        .select('name photo shopInfo metrics businessInfo')
        .lean()
    );
  } else {
    queries.push(Promise.resolve([]));
  }

  const [products, bales, shops] = await Promise.all(queries);

  const mapProducts = products.map(p => ({
    _id: p._id,
    type: p.type,
    name: p.name,
    images: p.images || [],
    basePrice: p.basePrice,
    status: p.status,
    maxDiscountPercentage: p.maxDiscountPercentage || 0,
    ratingsAverage: p.ratingsAverage || 0,
    ratingsQuantity: p.ratingsQuantity || 0,
    formattedPriceRange: p.formattedPriceRange,
    hasAnyDiscount: p.hasAnyDiscount || false,
    availableColors: p.availableColors || [],
    availableSizes: p.availableSizes || [],
    totalItemsLeft: p.totalStock || 0,
    brand: p.brand,
    gender: p.gender,
    shop: p.creator ? {
      name: p.creator.shopInfo?.name,
      logo: p.creator.shopInfo?.logo
    } : null
  }));

  const mapBales = bales.map(b => ({
    _id: b._id,
    type: b.type,
    name: b.name,
    images: b.images || [],
    basePrice: b.basePrice,
    status: b.status,
    maxDiscountPercentage: b.maxDiscountPercentage || 0,
    ratingsAverage: b.ratingsAverage || 0,
    ratingsQuantity: b.ratingsQuantity || 0,
    formattedPriceRange: b.formattedPriceRange,
    hasAnyDiscount: b.hasAnyDiscount || false,
    availableIdentifiers: b.availableIdentifiers || [],
    totalItemsLeft: b.totalStock || 0,
    country: b.country,
    condition: b.condition,
    totalItems: b.totalItems,
    weight: b.weight,
    shop: b.creator ? {
      name: b.creator.shopInfo?.name,
      logo: b.creator.shopInfo?.logo
    } : null
  }));

  const mapShops = shops.map(s => ({
    _id: s._id,
    type: 'shop',
    name: s.shopInfo?.name || s.businessInfo?.businessName || s.name,
    logo: s.shopInfo?.logo || s.photo,
    description: s.shopInfo?.description || '',
    followers: s.metrics?.followers || 0,
    metrics: {
      totalProducts: s.metrics?.totalProducts || 0,
      totalBales: s.metrics?.totalBales || 0,
      averageRating: s.metrics?.averageRating || 0,
      totalSales: s.metrics?.totalSales || 0,
      qualityScore: s.metrics?.qualityScore || 0
    }
  }));

  let total = 0;
  if (type === 'all') {
    const [productCount, baleCount, shopCount] = await Promise.all([
      Product.countDocuments(buildSearchFilters(req.query, 'product', searchRegex)),
      Product.countDocuments(buildSearchFilters(req.query, 'bale', searchRegex)),
      Creator.countDocuments({
        $or: [
          { name: searchRegex },
          { 'shopInfo.name': searchRegex },
          { 'shopInfo.description': searchRegex },
          { 'businessInfo.businessName': searchRegex }
        ],
        verificationStatus: 'verified',
        onboardingStatus: 'completed'
      })
    ]);
    total = productCount + baleCount + shopCount;
  } else {
    total = (type === 'products' ? products.length : type === 'bales' ? bales.length : shops.length);
  }

  // Handle special sorting (discount-desc, ending-soon)
  let allItems = [...mapProducts, ...mapBales];
  if (['discount-desc', 'ending-soon'].includes(req.query.sort) && type !== 'all') {
    allItems = allItems.sort((a, b) => (b.maxDiscountPercentage || 0) - (a.maxDiscountPercentage || 0));
    const sliced = allItems.slice(skip, skip + limit);
    if (type === 'products') mapProducts.splice(0, mapProducts.length, ...sliced.filter(i => i.type === 'product'));
    else if (type === 'bales') mapBales.splice(0, mapBales.length, ...sliced.filter(i => i.type === 'bale'));
  }

  res.status(200).json({
    status: 'success',
    results: mapProducts.length + mapBales.length + mapShops.length,
    total,
    page,
    limit,
    data: {
      products: mapProducts,
      bales: mapBales,
      shops: mapShops
    }
  });
});

// exports.search = catchAsync(async (req, res, next) => {
//   // Get search query
//   const query = req.query.q;
//   if (!query) {
//     return next(new AppError('Please provide a search query', 400));
//   }

//   // Get search type (default to 'all')
//   const type = req.query.type || 'all';

//   // Build base filters for products/bales
//   const baseFilters = { status: 'active' };

//   // Add category filter if provided
//   if (req.query.category) {
//     const categories = parseCommaSeparated(req.query.category);
//     if (categories.length > 0) {
//       baseFilters.$or = [
//         { category: { $in: categories } },
//         { relatedCategories: { $in: categories } }
//       ];
//     }
//   }

//   // Add price range filter if provided
//   if (req.query.minPrice || req.query.maxPrice) {
//     baseFilters.basePrice = {};
//     if (req.query.minPrice) {
//       baseFilters.basePrice.$gte = parseFloat(req.query.minPrice);
//     }
//     if (req.query.maxPrice) {
//       baseFilters.basePrice.$lte = parseFloat(req.query.maxPrice);
//     }
//   }

//   // Add creator filter if provided
//   if (req.query.creator) {
//     baseFilters.creator = req.query.creator;
//   }

//   // Add gender filter if provided (for products only)
//   if (req.query.gender) {
//     baseFilters.gender = req.query.gender;
//   }

//   // Add brands filter if provided (for products only)
//   if (req.query.brands) {
//     const brands = parseCommaSeparated(req.query.brands);
//     if (brands.length > 0) {
//       baseFilters.brand = { $in: brands };
//     }
//   }

//   // Add sizes filter if provided
//   if (req.query.sizes) {
//     const sizes = parseCommaSeparated(req.query.sizes);
//     if (sizes.length > 0) {
//       baseFilters['variations.size'] = { $in: sizes };
//     }
//   }

//   // Add countries filter if provided (for bales only)
//   if (req.query.countries) {
//     const countries = parseCommaSeparated(req.query.countries);
//     if (countries.length > 0) {
//       baseFilters.country = { $in: countries };
//     }
//   }

//   // Add conditions filter if provided (for bales only)
//   if (req.query.conditions) {
//     const conditions = parseCommaSeparated(req.query.conditions);
//     if (conditions.length > 0) {
//       baseFilters.condition = { $in: conditions };
//     }
//   }

//   // Create search regex
//   const searchRegex = new RegExp(query, 'i');

//   // Set pagination parameters
//   const page = parseInt(req.query.page) || 1;
//   const limit = parseInt(req.query.limit) || 20;
//   const skip = (page - 1) * limit;

//   // Set sort parameter (default to newest)
//   let sortBy = '-createdAt';
//   if (req.query.sort) {
//     switch (req.query.sort) {
//       case 'price-asc':
//         sortBy = 'basePrice';
//         break;
//       case 'price-desc':
//         sortBy = '-basePrice';
//         break;
//       case 'name-asc':
//         sortBy = 'name';
//         break;
//       case 'name-desc':
//         sortBy = '-name';
//         break;
//       case 'rating-desc':
//         sortBy = '-ratingsAverage';
//         break;
//       case 'discount-desc':
//         // Will handle discount sorting after fetching items
//         sortBy = '-createdAt'; // Default sort for now
//         break;
//       case 'ending-soon':
//         // Will handle ending soon sorting after fetching items
//         sortBy = '-createdAt'; // Default sort for now
//         break;
//       default:
//         sortBy = '-createdAt';
//     }
//   }

//   // Initialize results
//   let products = [];
//   let bales = [];
//   let shops = [];
//   let total = 0;

//   // Search products
//   if (type === 'all' || type === 'products') {
//     const productFilters = {
//       ...baseFilters,
//       type: 'product',
//       $or: [
//         { name: searchRegex },
//         { description: searchRegex },
//         { 'specifications.mainMaterial': searchRegex },
//         { brand: searchRegex },
//         { tags: searchRegex }
//       ]
//     };

//     // Remove bale-specific filters for products
//     delete productFilters.country;
//     delete productFilters.condition;

//     products = await Product.find(productFilters)
//       .populate('creator', 'shopInfo.name shopInfo.logo')
//       .sort(sortBy)
//       .skip(type === 'all' ? 0 : skip)
//       .limit(type === 'all' ? Math.floor(limit / 3) : limit)
//       .select('name brand basePrice images ratingsAverage ratingsQuantity gender variations type creator status');

//     if (type === 'products') {
//       total = await Product.countDocuments(productFilters);
//     }
//   }

//   // Search bales
//   if (type === 'all' || type === 'bales') {
//     const baleFilters = {
//       ...baseFilters,
//       type: 'bale',
//       $or: [
//         { name: searchRegex },
//         { description: searchRegex },
//         { country: searchRegex },
//         { tags: searchRegex }
//       ]
//     };

//     // Remove product-specific filters for bales
//     delete baleFilters.brand;
//     delete baleFilters.gender;

//     bales = await Product.find(baleFilters)
//       .populate('creator', 'shopInfo.name shopInfo.logo')
//       .sort(sortBy)
//       .skip(type === 'all' ? 0 : skip)
//       .limit(type === 'all' ? Math.floor(limit / 3) : limit)
//       .select('name basePrice images ratingsAverage ratingsQuantity country condition variations type creator totalItems weight status');

//     if (type === 'bales') {
//       total = await Product.countDocuments(baleFilters);
//     }
//   }

//   // Search shops/creators
//   if (type === 'all' || type === 'shops') {
//     const shopQuery = {
//       $or: [
//         { name: searchRegex },
//         { 'shopInfo.name': searchRegex },
//         { 'shopInfo.description': searchRegex },
//         { 'businessInfo.businessName': searchRegex }
//       ],
//       verificationStatus: 'verified',
//       onboardingStatus: 'completed'
//     };

//     shops = await Creator.find(shopQuery)
//       .sort('-metrics.followers')
//       .skip(type === 'all' ? 0 : skip)
//       .limit(type === 'all' ? Math.floor(limit / 3) : limit)
//       .select('name photo shopInfo metrics businessInfo');

//     if (type === 'shops') {
//       total = await Creator.countDocuments(shopQuery);
//     }
//   }

//   // Calculate total for 'all' type
//   if (type === 'all') {
//     // Build product count query
//     const productCountFilters = {
//       ...baseFilters,
//       type: 'product',
//       $or: [
//         { name: searchRegex },
//         { description: searchRegex },
//         { 'specifications.mainMaterial': searchRegex },
//         { brand: searchRegex },
//         { tags: searchRegex }
//       ]
//     };
//     delete productCountFilters.country;
//     delete productCountFilters.condition;

//     // Build bale count query
//     const baleCountFilters = {
//       ...baseFilters,
//       type: 'bale',
//       $or: [
//         { name: searchRegex },
//         { description: searchRegex },
//         { country: searchRegex },
//         { tags: searchRegex }
//       ]
//     };
//     delete baleCountFilters.brand;
//     delete baleCountFilters.gender;

//     const shopCountQuery = {
//       $or: [
//         { name: searchRegex },
//         { 'shopInfo.name': searchRegex },
//         { 'shopInfo.description': searchRegex },
//         { 'businessInfo.businessName': searchRegex }
//       ],
//       verificationStatus: 'verified',
//       onboardingStatus: 'completed'
//     };

//     const [productCount, baleCount, shopCount] = await Promise.all([
//       Product.countDocuments(productCountFilters),
//       Product.countDocuments(baleCountFilters),
//       Creator.countDocuments(shopCountQuery)
//     ]);

//     total = productCount + baleCount + shopCount;
//   }

//   // Process products to return enhanced data structure
//   const processedProducts = products.map(product => {
//     const productObj = product.toObject();

//     // Return enhanced product object with all virtuals
//     return {
//       _id: productObj._id,
//       type: productObj.type,
//       name: productObj.name,
//       images: productObj.images || [],
//       basePrice: productObj.basePrice,
//       status: productObj.status,
//       maxDiscountPercentage: product.maxDiscountPercentage || 0,
//       ratingsAverage: productObj.ratingsAverage || 0,
//       ratingsQuantity: productObj.ratingsQuantity || 0,
//       formattedPriceRange: product.formattedPriceRange,
//       hasAnyDiscount: product.hasAnyDiscount || false,
//       availableColors: product.availableColors || [],
//       availableSizes: product.availableSizes || [],
//       totalItemsLeft: product.totalStock || 0,
//       brand: productObj.brand,
//       gender: productObj.gender,
//       shop: productObj.creator ? {
//         name: productObj.creator.shopInfo?.name,
//         logo: productObj.creator.shopInfo?.logo
//       } : null
//     };
//   });

//   // Process bales to return enhanced data structure
//   const processedBales = bales.map(bale => {
//     const baleObj = bale.toObject();

//     // Return enhanced bale object with all virtuals
//     return {
//       _id: baleObj._id,
//       type: baleObj.type,
//       name: baleObj.name,
//       images: baleObj.images || [],
//       basePrice: baleObj.basePrice,
//       status: baleObj.status,
//       maxDiscountPercentage: bale.maxDiscountPercentage || 0,
//       ratingsAverage: baleObj.ratingsAverage || 0,
//       ratingsQuantity: baleObj.ratingsQuantity || 0,
//       formattedPriceRange: bale.formattedPriceRange,
//       hasAnyDiscount: bale.hasAnyDiscount || false,
//       availableIdentifiers: bale.availableIdentifiers || [], // For bales, use identifiers instead of colors/sizes
//       totalItemsLeft: bale.totalStock || 0,
//       country: baleObj.country,
//       condition: baleObj.condition,
//       totalItems: baleObj.totalItems,
//       weight: baleObj.weight,
//       shop: baleObj.creator ? {
//         name: baleObj.creator.shopInfo?.name,
//         logo: baleObj.creator.shopInfo?.logo
//       } : null
//     };
//   });

//   // Process shops to add type and format data
//   const processedShops = shops.map(shop => {
//     const shopObj = shop.toObject();

//     return {
//       _id: shopObj._id,
//       type: 'shop',
//       name: shopObj.shopInfo?.name || shopObj.businessInfo?.businessName || shopObj.name,
//       logo: shopObj.shopInfo?.logo || shopObj.photo,
//       description: shopObj.shopInfo?.description || '',
//       followers: shopObj.metrics?.followers || 0,
//       metrics: {
//         totalProducts: shopObj.metrics?.totalProducts || 0,
//         totalBales: shopObj.metrics?.totalBales || 0,
//         averageRating: shopObj.metrics?.averageRating || 0,
//         totalSales: shopObj.metrics?.totalSales || 0,
//         qualityScore: shopObj.metrics?.qualityScore || 0
//       }
//     };
//   });

//   // Apply special sorting if needed
//   let allItems = [...processedProducts, ...processedBales];

//   if (req.query.sort === 'discount-desc') {
//     allItems.sort((a, b) => {
//       const discountA = a.maxDiscountPercentage || 0;
//       const discountB = b.maxDiscountPercentage || 0;
//       return discountB - discountA;
//     });
//   } else if (req.query.sort === 'ending-soon') {
//     // First filter items on sale
//     const saleItems = allItems.filter(item => item.hasAnyDiscount);
//     const nonSaleItems = allItems.filter(item => !item.hasAnyDiscount);

//     // Sort sale items by discount percentage (highest first)
//     saleItems.sort((a, b) => (b.maxDiscountPercentage || 0) - (a.maxDiscountPercentage || 0));

//     // Combine sorted sale items with non-sale items
//     allItems = [...saleItems, ...nonSaleItems];
//   }

//   // Apply pagination if special sorting was used and not searching all types
//   if (['discount-desc', 'ending-soon'].includes(req.query.sort) && type !== 'all') {
//     const startIndex = skip;
//     const endIndex = skip + limit;

//     if (type === 'products') {
//       const productItems = allItems.filter(item => item.type === 'product');
//       processedProducts.splice(0, processedProducts.length, ...productItems.slice(startIndex, endIndex));
//     } else if (type === 'bales') {
//       const baleItems = allItems.filter(item => item.type === 'bale');
//       processedBales.splice(0, processedBales.length, ...baleItems.slice(startIndex, endIndex));
//     }
//   }

//   // Apply pagination for 'all' type or when no special sorting
//   if (type === 'all') {
//     // Limit results for 'all' type to show balanced results
//     const maxPerType = Math.floor(limit / 3);
//     processedProducts.splice(maxPerType);
//     processedBales.splice(maxPerType);
//     processedShops.splice(maxPerType);
//   } else if (!['discount-desc', 'ending-soon'].includes(req.query.sort)) {
//     // Apply normal pagination for single type searches without special sorting
//     if (type === 'products') {
//       const startIndex = skip;
//       const endIndex = skip + limit;
//       processedProducts.splice(0, processedProducts.length, ...processedProducts.slice(startIndex, endIndex));
//     } else if (type === 'bales') {
//       const startIndex = skip;
//       const endIndex = skip + limit;
//       processedBales.splice(0, processedBales.length, ...processedBales.slice(startIndex, endIndex));
//     } else if (type === 'shops') {
//       const startIndex = skip;
//       const endIndex = skip + limit;
//       processedShops.splice(0, processedShops.length, ...processedShops.slice(startIndex, endIndex));
//     }
//   }

//   res.status(200).json({
//     status: 'success',
//     results: processedProducts.length + processedBales.length + processedShops.length,
//     total,
//     page,
//     limit,
//     data: {
//       products: processedProducts,
//       bales: processedBales,
//       shops: processedShops
//     }
//   });
// });

