{"info": {"name": "Product Performance Optimization Tests", "description": "Test collection for optimized product endpoints with separated related/shop products", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}], "item": [{"name": "1. <PERSON><PERSON> as Buyer (Optional)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "2. Get Product (Optimized) - Main Product Only", "event": [{"listen": "test", "script": {"exec": ["pm.test('Product retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.product).to.exist;", "    ", "    // Store product ID for other requests", "    pm.collectionVariables.set('productId', response.data.product._id);", "});", "", "pm.test('Response structure is optimized', function () {", "    const response = pm.response.json();", "    ", "    // Should NOT contain related products or shop products", "    pm.expect(response.data.relatedProducts).to.be.undefined;", "    pm.expect(response.data.shopProducts).to.be.undefined;", "    ", "    // Should contain main product data", "    pm.expect(response.data.product).to.exist;", "    pm.expect(response.data.product.variations).to.exist;", "    pm.expect(response.data.product.shopInfo).to.exist;", "});", "", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000); // Should be under 1 second", "    console.log('Response time:', pm.response.responseTime + 'ms');", "});", "", "pm.test('Cart integration works (if authenticated)', function () {", "    const response = pm.response.json();", "    const product = response.data.product;", "    ", "    // If user is authenticated, cart info should be present", "    if (pm.collectionVariables.get('authToken')) {", "        // cartSummary can be null if no items in cart", "        pm.expect(product.cartSummary).to.satisfy(function(summary) {", "            return summary === null || (typeof summary === 'object' && summary.hasOwnProperty('totalVariationsInCart'));", "        });", "        ", "        // Each variation should have inCart property", "        if (product.variations) {", "            product.variations.forEach(variation => {", "                pm.expect(variation).to.have.property('inCart');", "            });", "        }", "    } else {", "        // Anonymous user should not have cart info", "        pm.expect(product.cartSummary).to.be.null;", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "disabled": false}], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}, {"name": "3. Get Related Products (Separate Endpoint)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Related products retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.products).to.exist;", "    pm.expect(response.results).to.be.a('number');", "});", "", "pm.test('Related products have correct structure', function () {", "    const response = pm.response.json();", "    const products = response.data.products;", "    ", "    if (products.length > 0) {", "        products.forEach(product => {", "            pm.expect(product).to.have.property('_id');", "            pm.expect(product).to.have.property('name');", "            pm.expect(product).to.have.property('images');", "            pm.expect(product).to.have.property('basePrice');", "            pm.expect(product).to.have.property('maxDiscountPercentage');", "            pm.expect(product).to.have.property('ratingsAverage');", "            pm.expect(product).to.have.property('ratingsQuantity');", "        });", "    }", "});", "", "pm.test('Response time is fast', function () {", "    pm.expect(pm.response.responseTime).to.be.below(800);", "    console.log('Related products response time:', pm.response.responseTime + 'ms');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}/related", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "related"]}}}, {"name": "4. Get Shop Products (Separate Endpoint)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Shop products retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.products).to.exist;", "    pm.expect(response.results).to.be.a('number');", "});", "", "pm.test('Shop products have correct structure', function () {", "    const response = pm.response.json();", "    const products = response.data.products;", "    ", "    if (products.length > 0) {", "        products.forEach(product => {", "            pm.expect(product).to.have.property('_id');", "            pm.expect(product).to.have.property('name');", "            pm.expect(product).to.have.property('images');", "            pm.expect(product).to.have.property('basePrice');", "            pm.expect(product).to.have.property('maxDiscountPercentage');", "            pm.expect(product).to.have.property('ratingsAverage');", "            pm.expect(product).to.have.property('ratingsQuantity');", "        });", "    }", "});", "", "pm.test('Response time is fast', function () {", "    pm.expect(pm.response.responseTime).to.be.below(800);", "    console.log('Shop products response time:', pm.response.responseTime + 'ms');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}/shop-products", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "shop-products"]}}}, {"name": "5. Performance Comparison - All Endpoints", "event": [{"listen": "test", "script": {"exec": ["pm.test('All endpoints respond successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Total time for all 3 requests should be reasonable', function () {", "    // This test will be updated based on the actual performance results", "    console.log('Individual endpoint approach allows for lazy loading and better UX');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}]}