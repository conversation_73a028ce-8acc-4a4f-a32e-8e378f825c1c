{"info": {"name": "Product Cart Integration Tests", "description": "Test collection for verifying product cart integration functionality", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "variationId1", "value": "", "type": "string"}, {"key": "variationId2", "value": "", "type": "string"}], "item": [{"name": "1. <PERSON><PERSON> as Buyer", "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.token).to.exist;", "    pm.collectionVariables.set('authToken', response.token);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "2. Get Product (Anonymous) - No Cart Info", "event": [{"listen": "test", "script": {"exec": ["pm.test('Product retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.product).to.exist;", "    ", "    // Store product and variation IDs for later use", "    const product = response.data.product;", "    pm.collectionVariables.set('productId', product._id);", "    ", "    if (product.variations && product.variations.length > 0) {", "        pm.collectionVariables.set('variationId1', product.variations[0]._id);", "        if (product.variations.length > 1) {", "            pm.collectionVariables.set('variationId2', product.variations[1]._id);", "        }", "    }", "});", "", "pm.test('No cart information for anonymous user', function () {", "    const response = pm.response.json();", "    const product = response.data.product;", "    ", "    // Cart summary should be null", "    pm.expect(product.cartSummary).to.be.null;", "    ", "    // Variations should not have cart info", "    if (product.variations) {", "        product.variations.forEach(variation => {", "            pm.expect(variation.inCart).to.be.null;", "        });", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}, {"name": "3. Add First Variation to Cart", "event": [{"listen": "test", "script": {"exec": ["pm.test('Product added to cart successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"variations\": [\n    {\n      \"variationId\": \"{{variationId1}}\",\n      \"quantity\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/cart/products", "host": ["{{baseUrl}}"], "path": ["cart", "products"]}}}, {"name": "4. Add Second Variation to Cart", "event": [{"listen": "test", "script": {"exec": ["pm.test('Second product added to cart successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"variations\": [\n    {\n      \"variationId\": \"{{variationId2}}\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/cart/products", "host": ["{{baseUrl}}"], "path": ["cart", "products"]}}}, {"name": "5. Get Product (Authenticated) - With Cart Info", "event": [{"listen": "test", "script": {"exec": ["pm.test('Product retrieved successfully with cart info', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.product).to.exist;", "});", "", "pm.test('Cart summary is present and correct', function () {", "    const response = pm.response.json();", "    const product = response.data.product;", "    ", "    // Cart summary should exist", "    pm.expect(product.cartSummary).to.exist;", "    pm.expect(product.cartSummary).to.not.be.null;", "    ", "    // Should have correct counts", "    pm.expect(product.cartSummary.totalVariationsInCart).to.be.at.least(1);", "    pm.expect(product.cartSummary.totalQuantityInCart).to.be.at.least(1);", "    ", "    console.log('Cart Summary:', JSON.stringify(product.cartSummary, null, 2));", "});", "", "pm.test('Variations have correct cart status', function () {", "    const response = pm.response.json();", "    const product = response.data.product;", "    ", "    if (product.variations) {", "        let variationsInCart = 0;", "        let totalQuantityInCart = 0;", "        ", "        product.variations.forEach((variation, index) => {", "            console.log(`Variation ${index + 1} (${variation._id}):`, {", "                color: variation.color,", "                size: variation.size,", "                inCart: variation.inCart", "            });", "            ", "            if (variation.inCart) {", "                variationsInCart++;", "                totalQuantityInCart += variation.inCart.quantity;", "                ", "                // Verify cart info structure", "                pm.expect(variation.inCart.quantity).to.be.a('number');", "                pm.expect(variation.inCart.addedAt).to.exist;", "            }", "        });", "        ", "        // Verify counts match cart summary", "        pm.expect(variationsInCart).to.eql(product.cartSummary.totalVariationsInCart);", "        pm.expect(totalQuantityInCart).to.eql(product.cartSummary.totalQuantityInCart);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}, {"name": "6. <PERSON><PERSON><PERSON> Cart Contents", "event": [{"listen": "test", "script": {"exec": ["pm.test('<PERSON><PERSON> retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql('success');", "    pm.expect(response.data.cart).to.exist;", "});", "", "pm.test('Cart contains expected items', function () {", "    const response = pm.response.json();", "    const cart = response.data.cart;", "    ", "    console.log('Cart contents:', JSON.stringify(cart, null, 2));", "    ", "    // Should have items", "    pm.expect(cart.itemCount).to.be.at.least(1);", "    pm.expect(cart.totalQuantity).to.be.at.least(1);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/cart", "host": ["{{baseUrl}}"], "path": ["cart"]}}}]}