#!/usr/bin/env node

/**
 * One-time script to fix creator metrics
 * This script recalculates and updates the metrics for creators who have zero counts
 * but actually have active products/bales
 *
 * Usage:
 * node scripts/fixCreatorMetrics.js [options]
 *
 * Options:
 * --all              Recalculate metrics for all creators
 * --creator-id=ID    Recalculate metrics for specific creator
 * --batch-size=N     Process N creators at a time (default: 10)
 * --help             Show this help message
 */

const mongoose = require('mongoose');
const { Creator } = require('../models/user.model');
const Product = require('../models/product.model');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: false,
  creatorId: null,
  batchSize: 10,
  help: false
};

args.forEach(arg => {
  if (arg === '--all') {
    options.all = true;
  } else if (arg === '--help') {
    options.help = true;
  } else if (arg.startsWith('--creator-id=')) {
    options.creatorId = arg.split('=')[1];
  } else if (arg.startsWith('--batch-size=')) {
    options.batchSize = parseInt(arg.split('=')[1]) || 10;
  }
});

// Show help
if (options.help) {
  console.log(`
Creator Metrics Fix Script

This script recalculates and updates the metrics for creators.

Usage:
  node scripts/fixCreatorMetrics.js [options]

Options:
  --all              Recalculate metrics for all creators
  --creator-id=ID    Recalculate metrics for specific creator
  --batch-size=N     Process N creators at a time (default: 10)
  --help             Show this help message

Examples:
  # Fix metrics for all creators with zero counts
  node scripts/fixCreatorMetrics.js

  # Fix metrics for all creators
  node scripts/fixCreatorMetrics.js --all

  # Fix metrics for specific creator
  node scripts/fixCreatorMetrics.js --creator-id=60f7b3b3b3b3b3b3b3b3b3b3

  # Process 20 creators at a time
  node scripts/fixCreatorMetrics.js --all --batch-size=20
`);
  process.exit(0);
}

async function fixCreatorMetrics(creatorId) {
  try {
    console.log(`🔧 Fixing metrics for creator: ${creatorId}`);

    // Get product and bale counts by status
    const [
      totalProducts,
      totalBales,
      activeProducts,
      activeBales,
      pendingProducts,
      pendingBales,
      inactiveProducts,
      inactiveBales
    ] = await Promise.all([
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'pending' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'pending' }),
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'inactive' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'inactive' })
    ]);

    // Prepare metrics update
    const metricsUpdate = {
      'metrics.totalProducts': totalProducts,
      'metrics.totalBales': totalBales,
      'metrics.activeProducts': activeProducts,
      'metrics.activeBales': activeBales,
      'metrics.pendingProducts': pendingProducts,
      'metrics.pendingBales': pendingBales,
      'metrics.inactiveProducts': inactiveProducts,
      'metrics.inactiveBales': inactiveBales
    };

    // Update creator metrics
    await Creator.findByIdAndUpdate(
      creatorId,
      { $set: metricsUpdate },
      { new: true, runValidators: true }
    );

    console.log(`✅ Updated metrics for creator ${creatorId}:`, {
      totalProducts,
      totalBales,
      activeProducts,
      activeBales,
      pendingProducts,
      pendingBales
    });

    return { success: true, totalProducts, totalBales };
  } catch (error) {
    console.error(`❌ Error fixing metrics for creator ${creatorId}:`, error.message);
    return { success: false, error: error.message };
  }
}

async function main() {
  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.DATABASE_URI || process.env.DATABASE_LOCAL);
    console.log('✅ Connected to MongoDB');

    if (options.creatorId) {
      // Fix metrics for specific creator
      console.log(`🎯 Fixing metrics for creator: ${options.creatorId}`);
      const result = await fixCreatorMetrics(options.creatorId);
      if (result.success) {
        console.log('✅ Metrics fixed successfully');
      } else {
        console.log('❌ Failed to fix metrics:', result.error);
      }
    } else {
      // Fix metrics for creators with zero counts
      const onlyZeroMetrics = !options.all;

      // Build query filter
      const filter = {};
      if (onlyZeroMetrics) {
        filter.$or = [
          { 'metrics.totalProducts': 0 },
          { 'metrics.totalBales': 0 },
          { 'metrics.totalProducts': { $exists: false } },
          { 'metrics.totalBales': { $exists: false } }
        ];
      }

      const creators = await Creator.find(filter).select('_id').lean();

      console.log(`📊 Found ${creators.length} creators to fix`);

      let processed = 0;
      let errors = 0;

      // Process creators in batches
      for (let i = 0; i < creators.length; i += options.batchSize) {
        const batch = creators.slice(i, i + options.batchSize);

        console.log(`Processing batch ${Math.floor(i / options.batchSize) + 1}/${Math.ceil(creators.length / options.batchSize)}`);

        const batchPromises = batch.map(async (creator) => {
          const result = await fixCreatorMetrics(creator._id);
          if (result.success) {
            processed++;
          } else {
            errors++;
          }
          return result;
        });

        await Promise.allSettled(batchPromises);

        // Add a small delay between batches
        if (i + options.batchSize < creators.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log('\n📊 Summary:');
      console.log(`Total creators: ${creators.length}`);
      console.log(`Successfully processed: ${processed}`);
      console.log(`Errors: ${errors}`);
      console.log('\n✅ Metrics fix completed!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('🔌 MongoDB connection closed');
    process.exit(0);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err.message);
  process.exit(1);
});

// Run the script
main();
